import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { QueueName } from '../../../shared/queue/queue.constants';
import { UserNodeExecuteDto, UserWorkflowExecuteDto } from '../dto/node-execute.dto';

/**
 * Service quản lý BullMQ queue cho workflow execution
 * Thay thế Redis Pub/Sub để có reliability và retry mechanism
 */
@Injectable()
export class WorkflowQueueService {
  private readonly logger = new Logger(WorkflowQueueService.name);

  constructor(
    @InjectQueue(QueueName.WORKFLOW_EXECUTION)
    private readonly workflowQueue: Queue,
  ) {}

  /**
   * Add node execution job to BullMQ queue
   */
  async executeUserNode(
    userId: number,
    workflowId: string,
    nodeId: string,
    type: 'test' | 'execute',
    inputData?: Record<string, any>
  ): Promise<any> {
    const payload: UserNodeExecuteDto = {
      userId,
      workflowId,
      nodeId,
      type,
      inputData
    };

    const job = await this.workflowQueue.add('execute_node', payload, {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: 100,
      removeOnFail: 50,
      priority: type === 'test' ? 10 : 5, // Test jobs có priority cao hơn
    });

    this.logger.log(`Node execution job added with ID: ${job.id}`);
    return { jobId: job.id, status: 'queued' };
  }

  /**
   * Add workflow execution job to BullMQ queue
   */
  async executeUserWorkflow(
    userId: number,
    workflowId: string,
    type: 'test' | 'execute',
    startFromNodeId?: string,
    inputData?: Record<string, any>
  ): Promise<any> {
    const payload: UserWorkflowExecuteDto = {
      userId,
      workflowId,
      nodeId: startFromNodeId || null,
      type,
      inputData
    };

    this.logger.log(`Adding workflow execution job to queue:`, {
      userId,
      workflowId,
      type,
      startFromNodeId
    });

    const job = await this.workflowQueue.add('execute_workflow', payload, {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 3000,
      },
      removeOnComplete: 100,
      removeOnFail: 50,
      priority: type === 'test' ? 10 : 5,
    });

    this.logger.log(`Workflow execution job added with ID: ${job.id}`);
    return { jobId: job.id, status: 'queued' };
  }

  /**
   * Get job status by ID
   */
  async getJobStatus(jobId: string): Promise<any> {
    const job = await this.workflowQueue.getJob(jobId);
    if (!job) {
      return { status: 'not_found' };
    }

    return {
      id: job.id,
      status: await job.getState(),
      progress: job.progress,
      data: job.data,
      returnvalue: job.returnvalue,
      failedReason: job.failedReason,
      processedOn: job.processedOn,
      finishedOn: job.finishedOn,
    };
  }

  /**
   * Cancel job by ID
   */
  async cancelJob(jobId: string): Promise<boolean> {
    const job = await this.workflowQueue.getJob(jobId);
    if (!job) {
      return false;
    }

    await job.remove();
    this.logger.log(`Job ${jobId} cancelled`);
    return true;
  }
}
