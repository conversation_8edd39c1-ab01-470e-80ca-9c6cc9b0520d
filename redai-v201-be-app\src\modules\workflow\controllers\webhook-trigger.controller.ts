import {
  All,
  Body,
  Controller,
  Headers,
  HttpCode,
  HttpException,
  HttpStatus,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>q,
  Re<PERSON>
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
import { Request, Response } from 'express';
import { Repository } from 'typeorm';
import { WebhookRegistry } from '../entities/webhook-registry.entity';
import { WorkflowQueueService } from '../services/workflow-queue.service';
import { WorkflowRedisService } from '../services/workflow-redis.service';
import { WorkflowSSEService } from '../user/services/workflow-sse-user.service';

@ApiTags('Webhook Triggers')
@Controller('/webhooks/:webhookId')
export class WebhookTriggerController {
  private readonly logger = new Logger(WebhookTriggerController.name);

  constructor(
    @InjectRepository(WebhookRegistry)
    private readonly webhookRegistryRepository: Repository<WebhookRegistry>,
    private readonly eventEmitter: EventEmitter2,
    private readonly workflowRedisService: WorkflowRedisService,
    private readonly workflowQueueService: WorkflowQueueService,
    private readonly workflowSSEService: WorkflowSSEService,
  ) { }

  @All()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Handle webhook trigger',
    description: 'Receives webhook data and triggers workflow execution',
  })
  @ApiParam({
    name: 'webhookId',
    description: 'Unique webhook identifier',
    type: 'string',
  })
  @ApiBody({
    description: 'Webhook payload data',
    schema: {
      type: 'object',
      example: {
        userId: 12345,
        action: 'created',
        timestamp: '2024-01-01T00:00:00Z',
        data: {
          id: 'item-123',
          name: 'Sample Item'
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Webhook processed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        webhookId: { type: 'string', example: 'webhook-123' },
        executionId: { type: 'string', example: 'exec-456' },
        timestamp: { type: 'string', example: '2024-01-01T00:00:00Z' }
      }
    }
  })
  async handleWebhook(
    @Param('webhookId') webhookId: string,
    @Req() request: Request,
    @Res() response: Response,
    @Body() body: Record<string, any>,
    @Headers() headers: Record<string, string>,
  ): Promise<void> {
    const startTime = Date.now();

    try {
      // 1. Validate webhook exists and load relations
      const webhook = await this.webhookRegistryRepository.findOne({
        where: { id: webhookId },
        relations: ['node', 'workflow'],
      });

      if (!webhook) {
        throw new HttpException(
          {
            error: 'Webhook not found',
            webhookId,
            timestamp: new Date().toISOString(),
          },
          HttpStatus.NOT_FOUND,
        );
      }

      // 2. Prepare webhook data
      const webhookData = {
        method: request.method,
        headers,
        body,
        query: request.query,
      };

      // 6. Check if user is online để determine execution mode
      const userId = webhook.workflow?.userId || 0;
      const isUserOnline = this.workflowSSEService.isUserOnline(userId);
      const executionMode = isUserOnline ? 'realtime' : 'background';

      if (executionMode === 'realtime') {
        this.eventEmitter.emit('webhook.received', {
          nodeId: webhook.nodeId,
          workflowId: webhook.workflowId,
          webhookData,
        });
      }
      
      // 10. Add webhook job to BullMQ queue (OPTIMIZED!)
      try {
        const webhookJobResult = await this.workflowQueueService.processWebhookWithQueue(
          webhookId,
          webhook.workflowId,
          webhook.nodeId,
          userId,
          {
            // Webhook data để pass vào workflow
            method: request.method,
            headers,
            body,
            query: request.query,
            sourceIp: request.ip,
            userAgent: request.get('User-Agent'),
            contentType: request.get('Content-Type'),
            timestamp: new Date().toISOString(),
            // Pre-completed webhook node data
            _preCompletedNodes: [{
              nodeId: webhook.nodeId,
              output: webhookNodeOutput,
              completedAt: new Date().toISOString()
            }],
            _executionMode: executionMode,
            _realtimeConfig: isUserOnline ? {
              publishEvents: true,
              channels: [`workflow.node.output.${webhook.workflowId}.*`, `workflow.status.${webhook.workflowId}`],
              userOnline: true,
            } : undefined,
          }
        );

        this.logger.log(`✅ Webhook job queued successfully: ${webhookId} (${executionMode} mode) - Job ID: ${webhookJobResult.jobId}`);
      } catch (queueError) {
        this.logger.error('Failed to queue webhook job:', queueError);
        // Continue với response, không block user
      }

      // 8. Return immediate response với execution mode info
      const responseTime = Date.now() - startTime;

      response.status(200).json({
        status: 'received',
        message: 'Webhook processed successfully',
        webhookId,
        webhookName: webhook.webhookName,
        timestamp: new Date().toISOString(),
        responseTime: `${responseTime}ms`,
        execution: {
          mode: executionMode,
          userOnline: isUserOnline,
          realtimeSupported: isUserOnline,
        },
        webhook: {
          id: webhook.id,
          name: webhook.webhookName,
          nodeId: webhook.nodeId,
          workflowId: webhook.workflowId,
          nodeName: webhook.node?.name,
          workflowName: webhook.workflow?.name,
        },
        request: {
          method: request.method,
          contentType: request.get('Content-Type'),
          bodyReceived: !!body,
          queryParams: Object.keys(request.query || {}).length,
          headers: Object.keys(headers).length,
          sourceIp: request.ip,
          userAgent: request.get('User-Agent'),
        },
      });

      console.log(`🚀 Webhook response sent:`, {
        webhookId,
        status: 200,
        responseTime: `${responseTime}ms`,
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;

      console.error(`💥 Webhook error:`, {
        webhookId,
        error: error.message,
        stack: error.stack,
        ip: request.ip,
        method: request.method,
        responseTime: `${responseTime}ms`,
        timestamp: new Date().toISOString(),
      });

      if (error instanceof HttpException) {
        response.status(error.getStatus()).json(error.getResponse());
      } else {
        response.status(500).json({
          error: 'Internal server error',
          webhookId,
          message: 'An unexpected error occurred while processing the webhook',
          timestamp: new Date().toISOString(),
          responseTime: `${responseTime}ms`,
        });
      }
    }
  }

  /**
   * Publish webhook node completion immediately for instant SSE feedback
   */
  private async publishWebhookNodeCompletion(workflowId: string, nodeId: string, nodeData: any): Promise<void> {
    try {
      await this.workflowRedisService.publishToRedis(
        `workflow.node.output.${workflowId}.${nodeId}`,
        {
          type: 'node.completed',
          workflowId,
          nodeId,
          ...nodeData,
          timestamp: new Date().toISOString()
        }
      );
      this.logger.log(`✅ Published webhook node completion immediately: ${nodeId}`);
    } catch (error) {
      this.logger.error('Failed to publish webhook node completion:', error);
    }
  }

  /**
   * Publish trigger event to Redis for immediate SSE feedback
   */
  private async publishTriggerEvent(workflowId: string, triggerData: any): Promise<void> {
    try {
      await this.workflowRedisService.publishToRedis(
        `workflow.trigger.${workflowId}`,
        {
          type: 'workflow.triggered',
          workflowId,
          ...triggerData,
          timestamp: new Date().toISOString()
        }
      );
      this.logger.debug(`Published trigger event for workflow ${workflowId}`);
    } catch (error) {
      this.logger.error('Failed to publish trigger event:', error);
    }
  }
}
